# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from higoalutils.config.preload import preload_utils
from higoalengine.config.preload import preload_engine


class PreloadManager:
    def __init__(self):
        pass
    async def preload_all(self):
        await preload_utils.start_up()
        await preload_engine.start_up()

    async def clean_up(self):
        await preload_utils.clean_up()
        await preload_engine.clean_up()

preload_manager = PreloadManager()
