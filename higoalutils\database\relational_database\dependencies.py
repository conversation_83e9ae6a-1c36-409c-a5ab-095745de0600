# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from sqlalchemy.ext.asyncio import AsyncSession
from typing import AsyncGenerator
from higoalutils.database.relational_database.manager import DBEngineManager

async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    sessionmaker = DBEngineManager().get_sessionmaker()
    async with sessionmaker() as session:
        yield session

async def get_db_session_ws() -> AsyncSession:
    sessionmaker = DBEngineManager().get_sessionmaker()
    return sessionmaker()
