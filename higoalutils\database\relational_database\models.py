# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from pydantic import BaseModel

from higoalutils.database.relational_database.enums import RelationDatabaseType


class RelationDatabaseConfig(BaseModel):
    type: RelationDatabaseType

class MysqlConfig(BaseModel):
    user: str
    password: str
    host: str
    port: int
    database: str

class SqliteConfig(BaseModel):
    path: str

class OBReConfig(BaseModel):
    user: str
    password: str
    host: str
    port: int
    database: str