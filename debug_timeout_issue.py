#!/usr/bin/env python3
"""
诊断超时问题的测试脚本
"""

import requests
import json
import time
import threading

# 配置
BASE_URL = "http://localhost:8000"
QA_ENDPOINT = f"{BASE_URL}/qa"

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    
    try:
        start_time = time.time()
        response = requests.get(f"{QA_ENDPOINT}/health", timeout=10)
        end_time = time.time()
        
        print(f"⏱️  健康检查耗时: {end_time - start_time:.3f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功: {data['message']}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_get_models():
    """测试获取模型列表"""
    print("🔍 测试获取模型列表...")
    
    try:
        start_time = time.time()
        response = requests.get(f"{QA_ENDPOINT}/getModel", timeout=10)
        end_time = time.time()
        
        print(f"⏱️  获取模型耗时: {end_time - start_time:.3f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 获取模型成功，模型数量: {len(data.get('data', []))}")
            return data.get("data", [])
        else:
            print(f"❌ 获取模型失败: {response.status_code} - {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 获取模型异常: {e}")
        return []

def test_sync_qa_with_monitoring(query: str, model_id: int, prompt_file: str = "basic_search"):
    """测试同步QA并监控整个过程"""
    print(f"🚀 测试同步QA任务...")
    print(f"   查询: {query}")
    print(f"   模型ID: {model_id}")
    print(f"   提示词: {prompt_file}")
    
    payload = {
        "query": query,
        "user_id": "debug_test",
        "model": model_id,
        "prompt_file": prompt_file
    }
    
    # 监控线程
    monitoring = {"stop": False, "start_time": time.time()}
    
    def monitor_progress():
        while not monitoring["stop"]:
            elapsed = time.time() - monitoring["start_time"]
            print(f"⏳ 请求进行中... 已耗时: {elapsed:.1f}秒")
            time.sleep(5)
    
    monitor_thread = threading.Thread(target=monitor_progress)
    monitor_thread.daemon = True
    monitor_thread.start()
    
    try:
        print("📤 发送请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{QA_ENDPOINT}/create_sync",
            json=payload,
            headers={
                "Content-Type": "application/json",
                "Connection": "keep-alive"
            },
            timeout=300,  # 5分钟超时
            stream=False
        )
        
        end_time = time.time()
        monitoring["stop"] = True
        duration = end_time - start_time
        
        print(f"📥 收到响应，总耗时: {duration:.2f}秒")
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📏 响应大小: {len(response.content)} 字节")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ QA任务成功")
                print(f"📝 任务ID: {data.get('data', {}).get('task_id', 'N/A')}")
                content = data.get('data', {}).get('content', '')
                print(f"📄 回答长度: {len(content)} 字符")
                print(f"📄 回答预览: {content[:200]}...")
                return data
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
                return None
        else:
            print(f"❌ QA任务失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        monitoring["stop"] = True
        print("❌ 请求超时")
        return None
    except requests.exceptions.ConnectionError as e:
        monitoring["stop"] = True
        print(f"❌ 连接错误: {e}")
        return None
    except Exception as e:
        monitoring["stop"] = True
        print(f"❌ 请求异常: {e}")
        return None

def test_connection_stability():
    """测试连接稳定性"""
    print("🔗 测试连接稳定性...")
    
    success_count = 0
    total_tests = 5
    
    for i in range(total_tests):
        print(f"📋 测试 {i+1}/{total_tests}")
        if test_health_check():
            success_count += 1
        time.sleep(1)
    
    print(f"📊 连接稳定性: {success_count}/{total_tests} ({success_count/total_tests*100:.1f}%)")
    return success_count == total_tests

def main():
    print("🧪 开始诊断超时问题")
    print("=" * 60)
    
    # 1. 测试连接稳定性
    if not test_connection_stability():
        print("❌ 连接不稳定，请检查服务状态")
        return
    
    print("\n" + "=" * 60)
    
    # 2. 测试基础接口
    models = test_get_models()
    if not models:
        print("❌ 无法获取模型列表")
        return
    
    print("\n" + "=" * 60)
    
    # 3. 测试简单查询
    model_id = models[0]["key"]
    simple_query = "你好"
    
    print(f"📝 使用模型: {models[0]['value']} (ID: {model_id})")
    print(f"🧪 测试简单查询: {simple_query}")
    
    result = test_sync_qa_with_monitoring(simple_query, model_id, "basic_search")
    
    if result:
        print("✅ 简单查询测试通过")
    else:
        print("❌ 简单查询测试失败")
        return
    
    print("\n" + "=" * 60)
    
    # 4. 测试复杂查询（类似原始问题）
    complex_query = "[PARAGRAPH_START:1] 甲方对上述条款有解释权，且有效性高于民法典。"
    
    print(f"🧪 测试复杂查询: {complex_query}")
    
    result = test_sync_qa_with_monitoring(complex_query, model_id, "hastur")
    
    if result:
        print("✅ 复杂查询测试通过")
    else:
        print("❌ 复杂查询测试失败")
    
    print("\n" + "=" * 60)
    print("🏁 诊断完成")

if __name__ == "__main__":
    main()
