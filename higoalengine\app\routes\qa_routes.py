# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from fastapi import APIRouter, Depends
import time
import asyncio
from typing import Annotated
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession

from higoalengine.config.models.api_models import *
from higoalutils.config.load_model_info import get_model_info
from higoalutils.database.relational_database.dependencies import get_db_session
from higoalutils.database.memory_store.dependencies import get_store_dep
from higoalutils.database.memory_store.base import MemoryStoreBase
from higoalutils.utils.code_utils.uuid import gen_uuid
from higoalengine.database.models import User<PERSON><PERSON><PERSON>, UserQuery
from higoalengine.app.tasks.qa_tasks import generate_answer
from higoalengine.config.enums.task_type import TaskStatusType
from higoalengine.config.models.api_chunk_models import FullResult
from higoalutils.storage.file_pipeline_storage import FilePipelineStorage
import os


router = APIRouter()


async def wait_for_task_completion(
    memory_store: MemoryStoreBase,
    task_id: str,
    timeout: float = 180.0,
    poll_interval: float = 0.5
) -> str | None:
    """
    等待任务完成并返回结果内容

    Args:
        memory_store: 内存存储实例
        task_id: 任务ID
        timeout: 超时时间（秒）
        poll_interval: 轮询间隔（秒）

    Returns:
        任务结果内容，如果失败或超时返回None
    """
    elapsed = 0.0
    status_key = f"task:{task_id}:status"
    result_key = f"task:{task_id}:result"

    while elapsed < timeout:
        status = await memory_store.get(status_key)

        if not status:
            print(f"⚠️ 任务 {task_id} 状态丢失")
            return None

        match status:
            case TaskStatusType.SUCCEEDED:
                result = await memory_store.get(result_key)
                if result:
                    full_result = FullResult.from_memory(result)
                    return full_result.content
                else:
                    print(f"⚠️ 任务 {task_id} 已完成但结果缺失")
                    return None

            case TaskStatusType.CANCELLED:
                print(f"⚠️ 任务 {task_id} 已取消")
                return None

            case TaskStatusType.FAILED:
                print(f"⚠️ 任务 {task_id} 执行失败")
                return None

            case TaskStatusType.PROCESSING | TaskStatusType.PENDING | TaskStatusType.OUTPUTTING:
                await asyncio.sleep(poll_interval)
                elapsed += poll_interval

            case _:
                print(f"⚠️ 任务 {task_id} 未知状态: {status}")
                return None

    print(f"⚠️ 任务 {task_id} 等待超时")
    return None

@router.get("/getModel", summary="获取 Chat 模型列表", tags=["模型管理"])
async def get_model():
    chat_models = get_model_info().get_chat_models()

    response_data = [
        {
            "key": model.id,
            "value": model.display_name
        }
        for model in chat_models
    ]

    return JSONResponse(content={
        "status": 1,
        "data": response_data,
        "msg": "操作成功",
        "timestamps": int(time.time() * 1000)
    })


@router.get("/getPrompts", summary="获取可用提示词列表", tags=["提示词管理"])
async def get_prompts():
    """
    获取datavolume/prompts目录下所有可用的提示词文件
    """
    try:
        prompt_storage = FilePipelineStorage("datavolume/prompts")

        # 获取所有yaml文件
        files = []
        prompt_dir = "datavolume/prompts"
        if os.path.exists(prompt_dir):
            for filename in os.listdir(prompt_dir):
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    # 去掉扩展名
                    name = os.path.splitext(filename)[0]
                    files.append({
                        "key": name,
                        "value": name,
                        "filename": filename
                    })

        return JSONResponse(content={
            "status": 1,
            "data": files,
            "msg": "操作成功",
            "timestamps": int(time.time() * 1000)
        })

    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": 0,
                "data": [],
                "msg": f"获取提示词列表失败: {str(e)}",
                "timestamps": int(time.time() * 1000)
            }
        )


@router.post("/create_sync", summary="同步创建QA任务", tags=["QA"])
async def create_sync_qa_task(
    req: HTTPCreateRequest,
    memory_store: Annotated[MemoryStoreBase, get_store_dep()],
    session: Annotated[AsyncSession, Depends(get_db_session)]
):
    """
    同步创建QA任务并等待完整结果返回

    与WebSocket版本的业务逻辑相同，但是：
    - 使用HTTP POST请求而不是WebSocket
    - 等待任务完成并返回完整结果
    - 不支持流式返回
    """
    task_id = gen_uuid()

    print(f"HTTP同步请求: {req}")

    # 创建数据库记录
    async with session.begin():
        session.add(UserQATask(task_id=task_id, user_id=req.user_id, status=TaskStatusType.PENDING))
        session.add(UserQuery(task_id=task_id, query_text=req.query))

    # 设置内存状态
    await memory_store.set(f"task:{task_id}:status", TaskStatusType.PENDING)

    # 获取模型名称
    model_name = get_model_info().get_by_id(req.model).model_name

    # 启动生成任务
    asyncio.create_task(generate_answer(task_id, req.query, model_name, req.prompt_file))

    # 等待任务完成
    result_content = await wait_for_task_completion(memory_store, task_id)

    if result_content is None:
        return JSONResponse(
            status_code=500,
            content={
                "status": 0,
                "code": 500,
                "message": "任务执行失败或超时",
                "data": None,
                "timestamps": int(time.time() * 1000)
            }
        )

    return JSONResponse(content={
        "status": 1,
        "code": 200,
        "message": "任务完成",
        "data": {
            "task_id": task_id,
            "content": result_content,
            "type": "result"
        },
        "timestamps": int(time.time() * 1000)
    })

