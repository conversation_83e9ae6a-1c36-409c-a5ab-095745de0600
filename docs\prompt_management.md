# 提示词管理指南

## 概述

HiGoalVita支持灵活的提示词切换功能，允许用户根据不同的业务场景使用不同的系统提示词，从而获得更精准的AI回答。

## 提示词文件结构

### 存储位置
所有提示词文件存储在 `datavolume/prompts/` 目录下。

### 文件格式

#### 1. YAML格式（推荐）
```yaml
answer: |
  # Role
  You are a helpful assistant...
  
  # Context
  {context_data}
  
  # Question
  {question}
```

#### 2. 纯文本格式
```text
你是一名专业的助手...

# 上下文
{context_data}

# 问题
{question}
```

### 变量占位符

提示词文件中可以使用以下占位符：

- `{context_data}`: 检索到的上下文信息
- `{question}`: 用户的查询问题
- `{language_code_var}`: 自动检测的语言代码

## 内置提示词

### 1. basic_search.yaml
- **用途**: 通用知识问答
- **特点**: 适用于一般性问题，提供信息性回答
- **格式**: YAML格式，使用`answer`键

### 2. hastur.yaml  
- **用途**: 合同法律风险分析
- **特点**: 专门用于合同条款的法律风险识别和分析
- **格式**: 纯文本格式
- **输出**: JSON格式的风险分析结果

## API接口

### 获取可用提示词列表

```http
GET /qa/getPrompts
```

**响应示例:**
```json
{
  "status": 1,
  "data": [
    {
      "key": "basic_search",
      "value": "basic_search",
      "filename": "basic_search.yaml"
    },
    {
      "key": "hastur", 
      "value": "hastur",
      "filename": "hastur.yaml"
    }
  ],
  "msg": "操作成功",
  "timestamps": 1234567890123
}
```

### 使用指定提示词创建任务

```http
POST /qa/create_sync
```

**请求参数:**
```json
{
  "query": "用户问题",
  "model": 1,
  "prompt_file": "hastur"
}
```

## 自定义提示词

### 创建新提示词

1. 在 `datavolume/prompts/` 目录下创建新的YAML文件
2. 使用以下模板：

```yaml
answer: |
  # Role
  你是一个专业的[领域]专家...
  
  # Objective  
  根据提供的上下文信息回答用户问题...
  
  # Context
  {context_data}
  
  # Question
  {question}
```

### 命名规范

- 文件名使用小写字母和下划线
- 文件扩展名为 `.yaml` 或 `.yml`
- 避免使用特殊字符和空格

### 示例：创建客服提示词

**文件名**: `customer_service.yaml`

```yaml
answer: |
  # Role
  你是一名专业的客服代表，友好、耐心且乐于助人。
  
  # Objective
  基于提供的知识库信息，为客户提供准确、有用的回答。
  如果信息不足，请诚实说明并建议客户联系人工客服。
  
  # Style
  - 使用友好、专业的语调
  - 回答要简洁明了
  - 提供具体的解决方案
  
  # Context
  {context_data}
  
  # Question
  {question}
```

## 使用示例

### Python客户端

```python
from higoal_client import HiGoalVitaClient

client = HiGoalVitaClient()

# 获取可用提示词
prompts = client.get_available_prompts()
print("可用提示词:", [p['key'] for p in prompts])

# 使用不同提示词
general_answer = client.ask("什么是AI？", prompt_file="basic_search")
legal_analysis = client.ask("分析合同风险", prompt_file="hastur")
service_response = client.ask("如何退款？", prompt_file="customer_service")
```

### CLI命令

```bash
# 使用默认提示词
higoalcore query --query "什么是机器学习？"

# 使用指定提示词
higoalcore query --query "分析合同条款" --prompt-file hastur

# 使用自定义提示词
higoalcore query --query "客服问题" --prompt-file customer_service
```

## 最佳实践

### 1. 提示词设计原则
- **明确角色定义**: 清楚说明AI的角色和专业领域
- **具体任务描述**: 详细说明期望的输出格式和内容
- **上下文利用**: 充分利用检索到的上下文信息
- **错误处理**: 处理信息不足的情况

### 2. 性能优化
- 避免过长的提示词，影响处理速度
- 使用清晰的结构和格式
- 测试不同场景下的效果

### 3. 版本管理
- 为重要的提示词创建备份
- 记录修改历史和原因
- 测试新版本的效果

## 故障排除

### 常见问题

1. **提示词文件不存在**
   - 检查文件路径和名称
   - 确认文件扩展名正确

2. **YAML格式错误**
   - 使用YAML验证工具检查语法
   - 注意缩进和特殊字符

3. **变量替换失败**
   - 检查占位符拼写
   - 确认变量名称正确

4. **输出格式不符合预期**
   - 调整提示词描述
   - 增加输出格式示例

### 调试技巧

- 使用简单的测试问题验证提示词
- 逐步增加复杂度
- 对比不同提示词的输出效果
