# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from fastapi import APIRouter
from higoalengine.app.routes.qa_routes import router as qa_router
from higoalengine.app.routes.health_routes import router as health_router

router = APIRouter()
router.include_router(qa_router, prefix="/qa", tags=["QA"])
router.include_router(health_router, prefix="/health", tags=["Health"])