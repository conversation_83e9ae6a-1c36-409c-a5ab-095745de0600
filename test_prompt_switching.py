#!/usr/bin/env python3
"""
测试提示词切换功能
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
QA_ENDPOINT = f"{BASE_URL}/qa"

def test_get_prompts():
    """测试获取提示词列表"""
    print("🔍 测试获取提示词列表...")
    
    response = requests.get(f"{QA_ENDPOINT}/getPrompts")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取提示词成功: {json.dumps(data, ensure_ascii=False, indent=2)}")
        return data.get("data", [])
    else:
        print(f"❌ 获取提示词失败: {response.status_code} - {response.text}")
        return []

def test_get_models():
    """测试获取模型列表"""
    print("🔍 测试获取模型列表...")
    
    response = requests.get(f"{QA_ENDPOINT}/getModel")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取模型成功")
        return data.get("data", [])
    else:
        print(f"❌ 获取模型失败: {response.status_code} - {response.text}")
        return []

def test_create_sync_qa_with_prompt(query: str, model_id: int, prompt_file: str, user_id: str = "test_user"):
    """测试使用指定提示词的同步QA任务"""
    print(f"🚀 测试同步QA任务(提示词: {prompt_file})...")
    print(f"   查询: {query}")
    print(f"   模型ID: {model_id}")
    print(f"   提示词: {prompt_file}")
    print(f"   用户ID: {user_id}")
    
    payload = {
        "query": query,
        "user_id": user_id,
        "model": model_id,
        "prompt_file": prompt_file
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{QA_ENDPOINT}/create_sync",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=180  # 3分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  请求耗时: {duration:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ QA任务成功")
            print(f"📝 回答内容: {data['data']['content'][:200]}...")  # 只显示前200字符
            return data
        else:
            print(f"❌ QA任务失败: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None

def main():
    print("🧪 开始测试提示词切换功能")
    print("=" * 60)
    
    # 1. 获取可用提示词
    prompts = test_get_prompts()
    if not prompts:
        print("❌ 无法获取提示词列表，测试终止")
        return
    
    print("\n" + "=" * 60)
    
    # 2. 获取可用模型
    models = test_get_models()
    if not models:
        print("❌ 无法获取模型列表，测试终止")
        return
    
    print("\n" + "=" * 60)
    
    # 3. 选择第一个可用模型进行测试
    model_id = models[0]["key"]
    model_name = models[0]["value"]
    print(f"📝 使用模型: {model_name} (ID: {model_id})")
    
    # 4. 测试不同提示词
    test_query = "[PARAGRAPH_START:1] 请分析以下合同条款的法律风险：甲方有权在任何时候无条件解除本合同，无需承担任何责任。"
    
    for i, prompt in enumerate(prompts, 1):
        prompt_name = prompt["key"]
        print(f"\n📋 测试 {i}/{len(prompts)} - 提示词: {prompt_name}")
        print("-" * 40)
        
        result = test_create_sync_qa_with_prompt(test_query, model_id, prompt_name)
        
        if result:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        # 间隔一下避免请求过快
        if i < len(prompts):
            print("⏳ 等待3秒...")
            time.sleep(3)
    
    print("\n" + "=" * 60)
    print("🏁 提示词切换测试完成")

if __name__ == "__main__":
    main()
