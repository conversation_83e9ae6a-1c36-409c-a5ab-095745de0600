#!/usr/bin/env python3
"""
HTTP同步接口客户端示例

这个示例展示了如何在另一个项目中调用HiGoalVita的HTTP同步接口
"""

import requests
import json
from typing import Optional, Dict, Any
import time

class HiGoalVitaClient:
    """HiGoalVita HTTP同步客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        Args:
            base_url: HiGoalVita服务的基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.qa_endpoint = f"{self.base_url}/qa"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'HiGoalVita-Client/1.0'
        })
    
    def get_available_models(self) -> Optional[list]:
        """
        获取可用的AI模型列表
        
        Returns:
            模型列表，格式为 [{"key": int, "value": str}, ...]
            如果失败返回None
        """
        try:
            response = self.session.get(f"{self.qa_endpoint}/getModel")
            response.raise_for_status()
            
            data = response.json()
            if data.get("status") == 1:
                return data.get("data", [])
            else:
                print(f"获取模型失败: {data.get('msg', '未知错误')}")
                return None
                
        except requests.RequestException as e:
            print(f"请求异常: {e}")
            return None

    def get_available_prompts(self) -> Optional[list]:
        """
        获取可用的提示词列表

        Returns:
            提示词列表，格式为 [{"key": str, "value": str, "filename": str}, ...]
            如果失败返回None
        """
        try:
            response = self.session.get(f"{self.qa_endpoint}/getPrompts")
            response.raise_for_status()

            data = response.json()
            if data.get("status") == 1:
                return data.get("data", [])
            else:
                print(f"获取提示词失败: {data.get('msg', '未知错误')}")
                return None

        except requests.RequestException as e:
            print(f"请求异常: {e}")
            return None

    def create_qa_task(
        self,
        query: str,
        model_id: int,
        user_id: str = "default",
        timeout: int = 180,
        prompt_file: str = "basic_search"
    ) -> Optional[Dict[str, Any]]:
        """
        创建QA任务并等待结果

        Args:
            query: 用户查询内容
            model_id: 模型ID
            user_id: 用户ID
            timeout: 超时时间（秒）
            prompt_file: 提示词文件名（不含扩展名）

        Returns:
            包含AI回答的字典，如果失败返回None
        """
        payload = {
            "query": query,
            "user_id": user_id,
            "model": model_id,
            "prompt_file": prompt_file
        }
        
        try:
            print(f"🚀 发送查询: {query}")
            start_time = time.time()
            
            response = self.session.post(
                f"{self.qa_endpoint}/create_sync",
                json=payload,
                timeout=timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            response.raise_for_status()
            
            data = response.json()
            if data.get("status") == 1:
                print(f"✅ 查询成功，耗时: {duration:.2f}秒")
                return data.get("data")
            else:
                print(f"❌ 查询失败: {data.get('message', '未知错误')}")
                return None
                
        except requests.Timeout:
            print("❌ 请求超时")
            return None
        except requests.RequestException as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def ask(self, question: str, model_id: Optional[int] = None, prompt_file: str = "basic_search") -> Optional[str]:
        """
        简化的问答接口

        Args:
            question: 问题
            model_id: 模型ID，如果不指定则使用第一个可用模型
            prompt_file: 提示词文件名（不含扩展名）

        Returns:
            AI的回答内容，如果失败返回None
        """
        # 如果没有指定模型，获取第一个可用模型
        if model_id is None:
            models = self.get_available_models()
            if not models:
                print("❌ 无法获取可用模型")
                return None
            model_id = models[0]["key"]
            print(f"📝 使用模型: {models[0]['value']} (ID: {model_id})")
        
        # 创建QA任务
        result = self.create_qa_task(question, model_id, prompt_file=prompt_file)
        if result:
            return result.get("content")
        return None

def main():
    """示例用法"""
    print("🤖 HiGoalVita HTTP同步客户端示例")
    print("=" * 50)
    
    # 创建客户端
    client = HiGoalVitaClient("http://localhost:8000")
    
    # 获取可用模型
    print("📋 获取可用模型...")
    models = client.get_available_models()
    if not models:
        print("❌ 无法获取模型列表")
        return

    print("✅ 可用模型:")
    for model in models:
        print(f"   - {model['value']} (ID: {model['key']})")

    # 获取可用提示词
    print("\n📋 获取可用提示词...")
    prompts = client.get_available_prompts()
    if not prompts:
        print("❌ 无法获取提示词列表")
        return

    print("✅ 可用提示词:")
    for prompt in prompts:
        print(f"   - {prompt['value']} ({prompt['filename']})")

    print("\n" + "=" * 50)
    
    # 示例问答 - 测试不同提示词
    questions_and_prompts = [
        ("你好，请介绍一下自己", "basic_search"),
        ("请分析以下合同条款的法律风险：甲方有权在任何时候无条件解除本合同", "hastur"),
        ("什么是机器学习？", "basic_search")
    ]

    model_id = models[0]["key"]

    for i, (question, prompt_file) in enumerate(questions_and_prompts, 1):
        print(f"\n📝 问题 {i}: {question}")
        print(f"🎯 使用提示词: {prompt_file}")
        print("-" * 50)

        answer = client.ask(question, model_id, prompt_file)
        if answer:
            print(f"🤖 回答: {answer[:300]}...")  # 只显示前300字符
        else:
            print("❌ 获取回答失败")

        # 避免请求过快
        if i < len(questions_and_prompts):
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("✅ 示例完成")

if __name__ == "__main__":
    main()
