#!/usr/bin/env python3
"""
测试新的HTTP同步接口
"""

import requests
import json
import time

# 配置
BASE_URL = "http://localhost:8000"
QA_ENDPOINT = f"{BASE_URL}/qa"

def test_get_models():
    """测试获取模型列表"""
    print("🔍 测试获取模型列表...")
    
    response = requests.get(f"{QA_ENDPOINT}/getModel")
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ 获取模型成功: {data}")
        return data.get("data", [])
    else:
        print(f"❌ 获取模型失败: {response.status_code} - {response.text}")
        return []

def test_create_sync_qa(query: str, model_id: int, user_id: str = "test_user"):
    """测试同步创建QA任务"""
    print(f"🚀 测试同步QA任务...")
    print(f"   查询: {query}")
    print(f"   模型ID: {model_id}")
    print(f"   用户ID: {user_id}")
    
    payload = {
        "query": query,
        "user_id": user_id,
        "model": model_id
    }
    
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{QA_ENDPOINT}/create_sync",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=180  # 3分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  请求耗时: {duration:.2f}秒")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ QA任务成功: {json.dumps(data, ensure_ascii=False, indent=2)}")
            return data
        else:
            print(f"❌ QA任务失败: {response.status_code} - {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return None

def main():
    print("🧪 开始测试HTTP同步接口")
    print("=" * 50)
    
    # 1. 获取可用模型
    models = test_get_models()
    if not models:
        print("❌ 无法获取模型列表，测试终止")
        return
    
    print("\n" + "=" * 50)
    
    # 2. 选择第一个可用模型进行测试
    model_id = models[0]["key"]
    model_name = models[0]["value"]
    print(f"📝 使用模型: {model_name} (ID: {model_id})")
    
    # 3. 测试同步QA
    test_queries = [
        "你好，请介绍一下自己",
        "什么是人工智能？",
        "请解释一下机器学习的基本概念"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}/{len(test_queries)}")
        print("-" * 30)
        result = test_create_sync_qa(query, model_id)
        
        if result:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")
        
        # 间隔一下避免请求过快
        if i < len(test_queries):
            print("⏳ 等待2秒...")
            time.sleep(2)
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
