# HTTP同步QA接口文档

## 概述

新增的HTTP同步接口提供了与WebSocket接口相同的业务逻辑，但采用传统的HTTP请求-响应模式，适合需要一次性获取完整结果的场景。

## 接口详情

### 1. 获取可用提示词列表

#### 请求信息
- **方法**: GET
- **URL**: `/qa/getPrompts`

#### 响应格式

##### 成功响应 (200)
```json
{
  "status": 1,
  "data": [
    {
      "key": "basic_search",
      "value": "basic_search",
      "filename": "basic_search.yaml"
    },
    {
      "key": "hastur",
      "value": "hastur",
      "filename": "hastur.yaml"
    }
  ],
  "msg": "操作成功",
  "timestamps": 1234567890123
}
```

### 2. 同步创建QA任务

#### 请求信息
- **方法**: POST
- **URL**: `/qa/create_sync`
- **Content-Type**: `application/json`

#### 请求参数

```json
{
  "query": "string",      // 必填，用户查询内容
  "user_id": "string",    // 可选，用户ID，默认为"default"
  "model": "integer",     // 必填，模型ID
  "prompt_file": "string" // 可选，提示词文件名（不含扩展名），默认为"basic_search"
}
```

#### 响应格式

##### 成功响应 (200)
```json
{
  "status": 1,
  "code": 200,
  "message": "任务完成",
  "data": {
    "task_id": "string",    // 任务ID
    "content": "string",    // AI生成的回答内容
    "type": "result"        // 响应类型
  },
  "timestamps": 1234567890123
}
```

##### 失败响应 (500)
```json
{
  "status": 0,
  "code": 500,
  "message": "任务执行失败或超时",
  "data": null,
  "timestamps": 1234567890123
}
```

## 与WebSocket接口的对比

| 特性 | WebSocket接口 | HTTP同步接口 |
|------|---------------|--------------|
| 连接方式 | 需要建立WebSocket连接 | 标准HTTP请求 |
| 返回方式 | 支持流式和非流式 | 仅一次性返回 |
| 实时性 | 可实时推送状态更新 | 等待完成后返回 |
| 复杂度 | 需要处理连接管理 | 简单的请求-响应 |
| 适用场景 | 实时交互、长连接 | 批量处理、API集成 |
| 超时设置 | 30秒（可配置） | 120秒（可配置） |

## 业务逻辑

HTTP同步接口与WebSocket接口共享相同的核心业务逻辑：

1. **任务创建**: 生成唯一任务ID，创建数据库记录
2. **状态管理**: 在内存存储中维护任务状态
3. **AI处理**: 调用相同的`generate_answer`函数
4. **结果获取**: 等待任务完成并返回结果

## 使用示例

### Python示例

```python
import requests

# 获取可用模型
models_response = requests.get("http://localhost:8000/qa/getModel")
models = models_response.json()["data"]
model_id = models[0]["key"]

# 获取可用提示词
prompts_response = requests.get("http://localhost:8000/qa/getPrompts")
prompts = prompts_response.json()["data"]

# 创建同步QA任务（使用默认提示词）
payload = {
    "query": "[PARAGRAPH_START:1] 机械设备租赁合同 编号：CJLQ2025090801 甲 方（承租方）：江山市春江沥青混凝土有限公司 乙 方（出租方）： 杭州氦狗科技有限公司 设备名称： 沥青路面摊铺机 2025 年 9 月 28 日 [PARAGRAPH_START:2] 机械设备租赁合同 合同编号：CJLQ2025090801 签订地点：沥青场 甲方(承租方)：江山市春江沥青混凝土有限公司 乙方(出租方)：杭州氦狗科技有限公司 [PARAGRAPH_START:3] 依照《中华人民共和国民法典》等相关法律规定，甲乙双方在自愿、平等、公平、诚实信用的基础上，就 沥青路面摊铺机 租赁事宜协商订立本合同： 第一条、机械设备名称、型号、规格及数量 1、上表中单价为固定含税综合单价，包括为完成工作任务所需的人工费、机械费、管理费、利润、保险等一切费用，主要包括但不仅限于设备进出场费、设备安装费、设备折旧费、设备维修保养费、设备操作维修人工费、设备燃油费、场地清理费、安全费、文明施工费、管理费、利润、乙方所有人员保险费等。 2、合同数量为暂定数量，结算数量以甲方签字确认的数量为准。乙方可根据现场情况与甲方的表现，单方面增加或减少甲方的机械设备租赁费用，甲方承诺无条件接受。 3、每台机械甲方需自行配备壹名操作人员，费用已含在综合单价中。 [PARAGRAPH_START:4] 4、若结算总金额超出上表中的暂定总金额，或结算项目在上表中无可参考单价时，视为超出合同范围，需甲乙双方同意签订补充协议后方可结算，否则超出合同约定范围不予结算。 第二条、租赁期限 1、设备租赁期限暂定 2025 年 9 月 28 日起至 2025 年 10 月 28 日止，实际租赁期限以乙方机械设备到场运转正常甲方办理签收手续之日起至机械设备使用完毕甲方下达退场通知之日止，乙方可根据现场情况与甲方的表现，单方面增加或减少甲方的机械设备租赁费用，甲方承诺无条件接受。 2、承租方因实际需要延长租期，应在合同届满前 5 日内，重新签订合同。 第三条、租金及结算 1、租金结算方式按以下第 3）种方式执行： [PARAGRAPH_START:5] 1）、按月租赁:租金 元/月/台。实际使用不足一个月部分按照月租费÷30.5天×实际使用天数计算。乙方每个月设备正常工作时间为 小时，超过部分按超时单价计算。 2）、按台班租赁: 租金 元/台班，每台班设备正常工作时间为 小时。 3）、按时租赁:租金 50 元/小时/台； 2、按月租赁的：因机械故障停机时间及节假日停工时间不计租金。 3、按台班租赁的：如因乙方设备故障或机手缺位等原因造成机械每台班实际工作时间低于每台班设备正常工作时间的，否则未满正常工作时间部分不做结算，按每台班单价除以设备正常工作时间折算扣除。 [PARAGRAPH_START:6] 4、按时租赁的：以机械实际工作时间为准，机械停机时间不作计价。 5、机械租赁:甲乙双方是租赁合同关系，甲方仅承担合同第一条的租赁费用，合同履行过程中发生的其他费用（如:修理费、保险费、年检费等）均由乙方自行承担。 6、机械实际工作时间必须经甲方现场负责人签字确认，其签字确认的书面凭证是租金结算的唯一参考依据。 7、结算单必须经甲方公司代表签字确认，其签字确认的结算单是租金支付的唯一依据。除此有效结算单外，其他任何证明、收条、欠条、信函等文件，都不得作为租金支付依据。 8、乙方全权委托: 陆昊辰 (身份证: 330183200708210024，联系电话: 15168218519)负责全权处理涉及本合同的所有事项，受托人 陆昊辰 签字的任何函件及结算单据乙方均予以认可,受托人从事在委托权限内的相关事务，由委托人承担责任。相关人员如有变动，由乙方公司书面通知甲方。 [PARAGRAPH_START:7] 第四条、租金支付 1、发票提供:乙方提供本合同约定内容的抵扣税率为 3% 的增值税专用发票。 2、支付时间：甲乙双方完成结算且甲方收到乙方开具的增值税发票后 5 天内。当乙方资金紧张时，甲方同意乙方适当减少支付比例，不构成违约，所有款项均不计利息。 3、支付要求：甲方支付租赁费前，乙方根据当月结算数量及金额提供正规合规的全额增值税发票。甲方按约定比例见票付款，开票单位、收款单位、品名、规格、单价与合同一致，数量与结算单一致，否则甲方不予付款。 4、如乙方无法按本合同约定提供足额的增值税专用发票且无法补足的，乙方应向甲方支付不足部分甲方的应纳税金补偿，甲方的应纳税金补偿=不足部分发票金额×（本合同约定抵扣税率+3%），以上税金补偿甲方可从乙方结算款中直接扣除。 [PARAGRAPH_START:8] 第五条、其他费用承担 1、机械燃油提供方式: 乙方提供 。 2、机械进出场费用承担: 进出场费均由乙方负责 。 3、机械的验收、安装、调试、使用、保养、维修管理等费用均由乙方承担。 第六条、租赁物要求 [PARAGRAPH_START:9] 1、租赁物必须证件齐全、性能良好、安全可靠。租赁物要适用施工现场条件； 2、乙方必须对租赁物进行必要的商业保险，第三者责任险保额要达到壹佰万元以上； 3、在合同期间对于乙方所属的租赁物经常出现故障、或有交通肇事、或不服从管理调度等影响甲方生产的，甲方有权随时予以清退，并且乙方应按甲方要求另行安排满足需求的租赁物。 4、租赁物必须具有有效的合格证，相关证件交甲方备案。 5、凡乙方安排参与本合同业务范围内的租赁标的物不论其所有者是否为乙方均视为乙方的机械设备，甲方仅与乙方进行业务处理及费用支付，所有者为第三方者必须事前出具《委托书》给甲方。如租赁物无《委托书》参与本合同租赁业务的，该租赁物产生的租赁费用将从乙方的租赁款中扣除，乙方与第三方之间产生的任何纠纷均由乙方承担责任与义务，与甲方无关。 [PARAGRAPH_START:10] 6、租赁期间机械设备的保管责任由乙方自行负责，严禁乱停放，造成的一切损失由乙方承担。 第七条、安全责任 1、乙方必须与所有操作人员签订安全责任书，并对其进行安全交底。 2、乙方要关注租赁物的安全性能，制止租赁物带病上路，严禁操作人员酒驾等不安全行为。 3、租赁期间因乙方以下原因造成交通安全事故和其他安全事故造成人身损害和财产损失，均由乙方承担赔偿责任： [PARAGRAPH_START:11] 1）、操作人员过失操作（不仅限于机手操作时玩手机、打电话等）。 2）、乙方机械设备故障。 3）、在工作运行过程中发生的一切交通事故和其他安全责任事故。 4）、其他乙方原因。 第九条、违约责任 [PARAGRAPH_START:12] 1、甲方设备一个月内连续出现故障三次，每次导致乙方无法正常作业四小时以上的，甲方有权解除合同。 2、机械到达现场之日不能正常运转的，乙方有权解除合同。 3、如按月租赁因故障造成单次连续停机超过一天以上，乙方有权另外承租同类型机械，所产生的费用应由甲方支付，乙方可在甲方实际应得的结算款中直接扣除。 4、未经甲方书面同意，乙方单方变更或解除合同的，应向甲方偿付本合同总租金10%的违约金。 第十条、争议解决 [PARAGRAPH_START:13] 本合同在履行过程中发生的争议，由双方当事人协商解决，协商不成双方同意向江山市人民法院起诉。 第十一条、附则 1、本合同履行期间，当事人双方不得随意变更或解除合同，合同如有未尽事宜，须经双方共同协商签订补充协议，补充协议双方确认签章后与本合同具有同等效力。 2、本合同一式肆份，甲方执叁份，乙方执壹份，自双方代表签字、盖章之日起生效。 3、附件：乙方营业执照、开户许可证及法人身份证信息、《安全生产责任书》。 [PARAGRAPH_START:14] 第十二条、特别声明 除甲方加盖公司公章的特别授权外，任何个人或组织均无权以甲方或甲方甲方或甲方项目负责人的名义对外借款、担保、收取押金（包括履约保证金）、处置资产等活动，对甲方均无约束力。 序号 名称与型号 数量 （台） [PARAGRAPH_START:15] 单位 含税单价 (元) 暂定租赁 时间 抵扣 税率 暂定金额 (元) [PARAGRAPH_START:16] 操作人员 （名） 备注 1 沥青路面摊铺机 1 [PARAGRAPH_START:17] 台 15,000 2025 年 9 月 28 日起至 2025 年 10 月 28 日 3% 36,000 [PARAGRAPH_START:18] 壹名/台 暂定金额合计 暂定金额合计 暂定金额合计 暂定金额合计 [PARAGRAPH_START:19] 暂定金额合计人民币大写 暂定金额合计人民币大写 暂定金额合计人民币大写 暂定金额合计人民币大写 不含税金额 [PARAGRAPH_START:20] 不含税金额 不含税金额 不含税金额 税金 税金 [PARAGRAPH_START:21] 税金 税金 甲方 乙方 单位名称：江山市春江沥青混凝土有限公司（盖章） 法定代表人： （或）委托代理人： 祝阳 （签字） 社会统一代码证或税号：91330881313694330L 开户银行：江山农商银行城关支行 帐号：201000128108073 电话：15168218519 签订日期： 2025 年 9 月 8 日 [PARAGRAPH_START:22] 单位名称：杭州氦狗科技有限公司（盖章） 法定代表人：陆昊辰 （或）委托代理人： （签字） 社会统一代码证或税号： 91330109MAECQBQF3E 开户银行： 杭州农商银行城关支行 帐 号：201000128108074 联系电话： 15168218519 签订日期：2025 年 9 月 8 日",
    "user_id": "user123",
    "model": model_id
}

response = requests.post(
    "http://localhost:8000/qa/create_sync",
    json=payload,
    timeout=180
)

if response.status_code == 200:
    result = response.json()
    print(f"AI回答: {result['data']['content']}")
else:
    print(f"请求失败: {response.text}")

# 使用自定义提示词
payload_custom = {
    "query": "请分析这个合同条款的风险",
    "user_id": "user123",
    "model": model_id,
    "prompt_file": "hastur"  # 使用hastur提示词
}

response_custom = requests.post(
    "http://localhost:8000/qa/create_sync",
    json=payload_custom,
    timeout=180
)
```

### JavaScript示例

```javascript
// 获取可用模型
const modelsResponse = await fetch('/qa/getModel');
const models = await modelsResponse.json();
const modelId = models.data[0].key;

// 创建同步QA任务
const payload = {
    query: "什么是人工智能？",
    user_id: "user123",
    model: modelId
};

const response = await fetch('/qa/create_sync', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
});

if (response.ok) {
    const result = await response.json();
    console.log('AI回答:', result.data.content);
} else {
    console.error('请求失败:', await response.text());
}
```

## 注意事项

1. **超时设置**: 默认超时时间为120秒，建议客户端设置合适的超时时间
2. **并发限制**: 建议控制并发请求数量，避免系统过载
3. **错误处理**: 请妥善处理超时和错误响应
4. **模型选择**: 使用`/qa/getModel`接口获取可用模型列表
5. **数据持久化**: 任务记录会保存到数据库中，便于后续查询和分析

## 性能考虑

- 响应时间取决于AI模型的处理速度和查询复杂度
- 建议在生产环境中配置适当的负载均衡和缓存策略
- 可以通过调整轮询间隔和超时时间来优化性能
