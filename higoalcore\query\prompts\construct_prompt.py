# Copyright 2025 HiGoal Corporation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""提示词构建模块。"""

import yaml

from higoalcore.config.enums.query_enums import PromptFileType, SearchMethod
from higoalcore.query.prompts.prompt_types import BasicSearch
from higoalutils.storage.file_pipeline_storage import FilePipelineStorage
from higoalutils.utils.language_utils.detect_language import detect_language


async def get_prompt_template(
    query_text: str,
    search_method: SearchMethod = SearchMethod.BASIC,
    root_dir: str = "datavolume",
    prompt_dir: str = "prompts",
    language_threshold: float = 0.3,
    prompt_file_type: PromptFileType = PromptFileType.YAML,
    prompt_file: str | None = None,
) -> str:
    file_path = FilePipelineStorage(f"{root_dir}/{prompt_dir}")
    prompt_file_name = ""
    if prompt_file_type == PromptFileType.YAML:
        extention = PromptFileType.YAML.value
    else:
        extention = PromptFileType.TXT.value

    # 如果指定了自定义提示词文件，使用自定义文件
    if prompt_file:
        prompt_file_name = f"{prompt_file}{extention}"
        key = BasicSearch.prompt.value  # 默认使用answer键
    else:
        # 使用默认的提示词文件
        match search_method:
            case search_method.BASIC:
                prompt_file_name = f"{BasicSearch.file.value}{extention}"
                key = BasicSearch.prompt.value
    
    if not await file_path.has(prompt_file_name):
        raise ValueError(f"Prompt file {file_path}/{prompt_file_name} does not exist!")
    prompt = await file_path.get(prompt_file_name)
    if not prompt:
        raise ValueError(f"Prompt file {file_path}/{prompt_file_name} is empty!")
    
    language_code = detect_language(query_text, language_threshold) 

    if prompt_file_type == PromptFileType.YAML:
        try:
            prompt_dict = yaml.safe_load(prompt)

            # 如果是自定义提示词文件，尝试不同的处理方式
            if prompt_file:
                # 如果YAML解析结果是字典且包含answer键
                if isinstance(prompt_dict, dict) and key in prompt_dict and prompt_dict[key]:
                    prompt = prompt_dict[key].replace("{language_code_var}", language_code)
                # 如果YAML解析结果是字典但没有answer键，尝试使用第一个非空值
                elif isinstance(prompt_dict, dict):
                    for k, v in prompt_dict.items():
                        if v and isinstance(v, str):
                            prompt = v.replace("{language_code_var}", language_code)
                            break
                    else:
                        # 如果字典中没有找到合适的值，可能是纯文本文件被误认为YAML
                        prompt = prompt.replace("{language_code_var}", language_code)
                # 如果YAML解析结果是字符串（纯文本文件）
                elif isinstance(prompt_dict, str):
                    prompt = prompt_dict.replace("{language_code_var}", language_code)
                # 如果YAML解析失败或结果为None，直接使用原始文本
                else:
                    prompt = prompt.replace("{language_code_var}", language_code)
            else:
                # 默认提示词文件的处理
                if not prompt_dict[key]: # type: ignore
                    raise ValueError(f"Prompt file {file_path}/{prompt_file_name} prompt {key} is empty!") # type: ignore
                prompt = prompt_dict[key].replace("{language_code_var}", language_code) # type: ignore
        except yaml.YAMLError:
            # 如果YAML解析失败，当作纯文本处理
            prompt = prompt.replace("{language_code_var}", language_code)
    else:
        prompt = prompt.replace("{language_code_var}", language_code)
    return prompt

def complete_prompt(
    query: str,
    prompt: str,
    context: str,
    response_type: str | None = None
) -> list[str]:
    """构建完整的提示词"""
    messages = []
    final_prompt = prompt
    if not response_type:
        final_prompt = final_prompt.replace("{response_type}","文本以Markdown格式回答")
    else:
        final_prompt = final_prompt.replace("{response_type}",response_type)
    final_prompt = final_prompt.replace("{context_data}",context)
    final_prompt = final_prompt.replace("{question}",query)
    messages.append({"role": "user", "content": final_prompt})
    return messages  
